import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'
import { notificationService } from './notifications'
import { storageService } from './storage'
import type { Tables } from '@/lib/supabase'

export interface MessageData {
  id?: string
  conversation_id: string
  sender_id: string
  type: 'text' | 'image' | 'file' | 'system'
  content: string
  attachments?: MessageAttachment[]
  reply_to?: string
  sent_at?: string
  read_at?: string
}

export interface MessageAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
  thumbnail?: string
}

export interface ConversationData {
  id?: string
  project_id: string
  participants: string[]
  last_message_id?: string
  unread_count: number
  created_at?: string
  updated_at?: string
}

export interface ConversationWithDetails extends ConversationData {
  project?: {
    id: string
    title: string
    status: string
  }
  participants_data?: {
    id: string
    name: string
    avatar_url?: string
    role: string
  }[]
  last_message?: MessageData
}

export class MessagingService {
  // Conversation management
  async createConversation(projectId: string, participants: string[]): Promise<ServiceResponse<ConversationData>> {
    try {
      // Validate inputs
      if (!projectId) {
        console.error('createConversation: projectId is required')
        return createResponse(null, 'Project ID is required')
      }

      if (!participants || participants.length === 0) {
        console.error('createConversation: participants array is required and cannot be empty')
        return createResponse(null, 'Participants are required')
      }

      // Filter out any null/undefined participants and validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      const validParticipants = participants.filter(p => {
        if (!p || typeof p !== 'string') return false
        return uuidRegex.test(p)
      })

      if (validParticipants.length === 0) {
        console.error('createConversation: no valid participants found', {
          originalParticipants: participants,
          filteredParticipants: validParticipants
        })
        return createResponse(null, 'No valid participants provided')
      }

      console.log('Creating conversation for project:', projectId, 'with participants:', validParticipants)

      // Check if conversation already exists for this project
      const { data: existing, error: existingError } = await supabase
        .from('conversations')
        .select('*')
        .eq('project_id', projectId)
        .single()

      if (existingError && existingError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected if no conversation exists
        console.error('Error checking existing conversation:', existingError)
        return createResponse(null, existingError.message || 'Failed to check existing conversation')
      }

      if (existing) {
        console.log('Found existing conversation:', existing.id)
        return createResponse(existing)
      }

      // Create new conversation
      const conversationData = {
        project_id: projectId,
        participants: validParticipants,
        unread_count: 0
      }

      console.log('Inserting conversation data:', conversationData)

      const { data, error } = await supabase
        .from('conversations')
        .insert(conversationData)
        .select()
        .single()

      if (error) {
        const errorDetails = {
          error,
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          conversationData,
          timestamp: new Date().toISOString()
        }
        console.error('Error creating conversation:', errorDetails)

        // Provide more specific error messages based on error code
        let errorMessage = 'Failed to create conversation'
        if (error.code === '23505') {
          errorMessage = 'A conversation for this project already exists'
        } else if (error.code === '23503') {
          errorMessage = 'Invalid project or user reference'
        } else if (error.message) {
          errorMessage = error.message
        }

        return createResponse(null, errorMessage)
      }

      console.log('Successfully created conversation:', data)
      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in createConversation:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        projectId,
        participants
      })
      return createResponse(null, 'An unexpected error occurred while creating conversation')
    }
  }

  async getUserConversations(userId: string): Promise<ServiceResponse<ConversationWithDetails[]>> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          projects!project_id (
            id,
            title,
            status
          ),
          messages!last_message_id (
            id,
            content,
            type,
            sent_at,
            users!sender_id (
              name
            )
          )
        `)
        .contains('participants', [userId])
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching conversations:', error)
        return createResponse(null, error.message)
      }

      // Get participant details for each conversation
      const conversationsWithDetails = await Promise.all(
        (data || []).map(async (conversation) => {
          const participantIds = conversation.participants.filter((id: string) => id !== userId)
          
          const { data: participantsData } = await supabase
            .from('users')
            .select('id, name, avatar_url, role')
            .in('id', participantIds)

          return {
            ...conversation,
            participants_data: participantsData || []
          }
        })
      )

      return createResponse(conversationsWithDetails)
    } catch (error) {
      console.error('Unexpected error in getUserConversations:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async getConversation(conversationId: string): Promise<ServiceResponse<ConversationWithDetails>> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          projects!project_id (
            id,
            title,
            status
          )
        `)
        .eq('id', conversationId)
        .single()

      if (error) {
        console.error('Error fetching conversation:', error)
        return createResponse(null, error.message)
      }

      // Get participant details
      const { data: participantsData } = await supabase
        .from('users')
        .select('id, name, avatar_url, role')
        .in('id', data.participants)

      return createResponse({
        ...data,
        participants_data: participantsData || []
      })
    } catch (error) {
      console.error('Unexpected error in getConversation:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Message management
  async sendMessage(messageData: Omit<MessageData, 'id' | 'sent_at'>): Promise<ServiceResponse<MessageData>> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: messageData.conversation_id,
          sender_id: messageData.sender_id,
          type: messageData.type,
          content: messageData.content,
          attachments: messageData.attachments || [],
          reply_to: messageData.reply_to,
          status: 'sent'
        })
        .select(`
          *,
          users!sender_id (
            id,
            name,
            avatar_url,
            role
          )
        `)
        .single()

      if (error) {
        console.error('Error sending message:', error)
        return createResponse(null, error.message)
      }

      // Update conversation with last message and increment unread count
      await this.updateConversationLastMessage(messageData.conversation_id, data.id)

      // Send real-time notification
      await this.broadcastMessage(messageData.conversation_id, data)

      // Send push notification to other participants
      await this.notifyParticipants(messageData.conversation_id, messageData.sender_id, data)

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in sendMessage:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async getMessages(conversationId: string, limit: number = 50, offset: number = 0): Promise<ServiceResponse<MessageData[]>> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          users!sender_id (
            id,
            name,
            avatar_url,
            role
          )
        `)
        .eq('conversation_id', conversationId)
        .order('sent_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching messages:', error)
        return createResponse(null, error.message)
      }

      return createResponse((data || []).reverse()) // Reverse to show oldest first
    } catch (error) {
      console.error('Unexpected error in getMessages:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async markMessagesAsRead(conversationId: string, userId: string): Promise<ServiceResponse<void>> {
    try {
      // Mark all unread messages in conversation as read
      const { error } = await supabase
        .from('messages')
        .update({ 
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('conversation_id', conversationId)
        .neq('sender_id', userId)
        .is('read_at', null)

      if (error) {
        console.error('Error marking messages as read:', error)
        return createResponse(null, error.message)
      }

      // Reset unread count for this user in conversation
      await this.resetUnreadCount(conversationId, userId)

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in markMessagesAsRead:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Real-time functionality
  subscribeToConversation(conversationId: string, callback: (message: MessageData) => void) {
    const channel = supabase
      .channel(`conversation:${conversationId}`)
      .on('broadcast', { event: 'new_message' }, (payload) => {
        callback(payload.payload)
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  subscribeToUserConversations(userId: string, callback: (conversation: ConversationData) => void) {
    const channel = supabase
      .channel(`user_conversations:${userId}`)
      .on('broadcast', { event: 'conversation_updated' }, (payload) => {
        callback(payload.payload)
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  // Typing indicators
  broadcastTyping(conversationId: string, userId: string, isTyping: boolean) {
    const channel = supabase.channel(`conversation:${conversationId}`)
    return channel.send({
      type: 'broadcast',
      event: 'typing',
      payload: { userId, isTyping, timestamp: new Date().toISOString() }
    })
  }

  subscribeToTyping(conversationId: string, callback: (data: { userId: string, isTyping: boolean }) => void) {
    const channel = supabase
      .channel(`conversation:${conversationId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        callback(payload.payload)
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  // User presence
  updateUserPresence(userId: string, isOnline: boolean) {
    const channel = supabase.channel('user_presence')
    return channel.send({
      type: 'broadcast',
      event: 'presence_update',
      payload: { userId, isOnline, timestamp: new Date().toISOString() }
    })
  }

  subscribeToUserPresence(callback: (data: { userId: string, isOnline: boolean }) => void) {
    const channel = supabase
      .channel('user_presence')
      .on('broadcast', { event: 'presence_update' }, (payload) => {
        callback(payload.payload)
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  // File upload for message attachments
  async uploadMessageAttachment(file: File, conversationId: string): Promise<ServiceResponse<MessageAttachment>> {
    try {
      const result = await storageService.uploadFile(file, {
        bucket: 'message-attachments',
        folder: `conversations/${conversationId}`,
        maxSize: 50 * 1024 * 1024, // 50MB for message attachments
        allowedTypes: [
          'image/jpeg', 'image/png', 'image/webp', 'image/gif',
          'application/pdf', 'text/plain', 'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]
      })

      if (result.success && result.data) {
        const attachment: MessageAttachment = {
          id: crypto.randomUUID(),
          name: file.name,
          url: result.data.url,
          type: file.type,
          size: file.size,
          thumbnail: file.type.startsWith('image/') ? result.data.url : undefined
        }

        return createResponse(attachment)
      } else {
        return createResponse(null, result.error || 'Failed to upload attachment')
      }
    } catch (error) {
      console.error('Error uploading message attachment:', error)
      return createResponse(null, 'An unexpected error occurred during upload')
    }
  }

  // Upload multiple attachments
  async uploadMessageAttachments(files: File[], conversationId: string): Promise<ServiceResponse<MessageAttachment[]>> {
    try {
      const attachments: MessageAttachment[] = []
      const errors: string[] = []

      for (const file of files) {
        const result = await this.uploadMessageAttachment(file, conversationId)
        if (result.success && result.data) {
          attachments.push(result.data)
        } else {
          errors.push(`${file.name}: ${result.error}`)
        }
      }

      if (errors.length > 0 && attachments.length === 0) {
        return createResponse(null, `All uploads failed: ${errors.join(', ')}`)
      }

      if (errors.length > 0) {
        console.warn('Some attachment uploads failed:', errors)
      }

      return createResponse(attachments)
    } catch (error) {
      console.error('Error uploading message attachments:', error)
      return createResponse(null, 'An unexpected error occurred during batch upload')
    }
  }

  // Private helper methods
  private async updateConversationLastMessage(conversationId: string, messageId: string): Promise<void> {
    try {
      await supabase
        .from('conversations')
        .update({
          last_message_id: messageId,
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId)
    } catch (error) {
      console.error('Error updating conversation last message:', error)
    }
  }

  private async broadcastMessage(conversationId: string, message: MessageData): Promise<void> {
    try {
      await supabase
        .channel(`conversation:${conversationId}`)
        .send({
          type: 'broadcast',
          event: 'new_message',
          payload: message
        })
    } catch (error) {
      console.error('Error broadcasting message:', error)
    }
  }

  private async notifyParticipants(conversationId: string, senderId: string, message: MessageData): Promise<void> {
    try {
      // Get conversation participants (excluding sender)
      const { data: conversation } = await supabase
        .from('conversations')
        .select('participants, projects!project_id(title)')
        .eq('id', conversationId)
        .single()

      if (!conversation) return

      const recipients = conversation.participants.filter((id: string) => id !== senderId)
      
      // Get sender info
      const { data: sender } = await supabase
        .from('users')
        .select('name')
        .eq('id', senderId)
        .single()

      // Send notifications to each recipient
      for (const recipientId of recipients) {
        await notificationService.notifyNewMessage(
          recipientId,
          sender?.name || 'Someone',
          (conversation.projects as any)?.title || 'Project'
        )
      }
    } catch (error) {
      console.error('Error notifying participants:', error)
    }
  }

  private async resetUnreadCount(conversationId: string, userId: string): Promise<void> {
    // This would require a more complex implementation to track per-user unread counts
    // For now, we'll just reset the global unread count
    try {
      await supabase
        .from('conversations')
        .update({ unread_count: 0 })
        .eq('id', conversationId)
    } catch (error) {
      console.error('Error resetting unread count:', error)
    }
  }
}

// Export singleton instance
export const messagingService = new MessagingService()
