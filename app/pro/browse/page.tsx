"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { ProRoute } from "@/components/route-guard"
import { BookmarkButton, LikeButton } from "@/components/ui/interactive-elements"
import { 
  Search, 
  MapPin, 
  Calendar, 
  DollarSign, 
  User, 
  Clock, 
  Filter,
  Heart,
  Send,
  Eye,
  Star,
  Bookmark,
  ChevronDown
} from "lucide-react"

interface Project {
  id: string
  title: string
  description: string
  category: string
  budget: string
  timeline: string
  location: string
  postedDate: string
  client: {
    name: string
    rating: number
    reviewCount: number
    verified: boolean
  }
  requirements: string[]
  bidsCount: number
  status: "open" | "in-review" | "awarded" | "closed"
  urgency: "low" | "medium" | "high"
  images?: string[]
}

export default function BrowseProjectsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedBudget, setSelectedBudget] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true)
        // TODO: Replace with actual project service call
        // const response = await projectService.getAvailableProjects({
        //   status: 'active',
        //   sortBy: 'created_at',
        //   sortOrder: 'desc'
        // })

        // For now, use empty array until database integration is complete
        setProjects([])
        setLoading(false)
      } catch (error) {
        console.error("Error loading projects:", error)
        setProjects([])
        setLoading(false)
      }
    }

    loadProjects()
  }, [])

  // Mock projects data - keeping structure for reference
  const mockProjects: Project[] = [
    {
      id: "1",
      title: "Modern Kitchen Renovation",
      description: "Complete kitchen remodel including new cabinets, countertops, appliances, and flooring. Looking for a contractor with experience in modern design and quality craftsmanship.",
      category: "Kitchen Remodeling",
      budget: "$25,000 - $35,000",
      timeline: "6-8 weeks",
      location: "San Francisco, CA",
      postedDate: "2024-01-10",
      client: {
        name: "Sarah Johnson",
        rating: 4.8,
        reviewCount: 12,
        verified: true
      },
      requirements: ["Licensed contractor", "Insurance required", "References needed"],
      bidsCount: 8,
      status: "open",
      urgency: "medium"
    },
    {
      id: "2",
      title: "Bathroom Renovation - Master Suite",
      description: "Master bathroom renovation including walk-in shower, double vanity, and luxury finishes. High-end project requiring attention to detail.",
      category: "Bathroom Renovation",
      budget: "$15,000 - $25,000",
      timeline: "4-6 weeks",
      location: "Oakland, CA",
      postedDate: "2024-01-12",
      client: {
        name: "Mike Chen",
        rating: 4.9,
        reviewCount: 8,
        verified: true
      },
      requirements: ["Licensed contractor", "Portfolio required", "Insurance required"],
      bidsCount: 5,
      status: "open",
      urgency: "high"
    },
    {
      id: "3",
      title: "Hardwood Flooring Installation",
      description: "Install hardwood flooring throughout main living areas (approximately 1,200 sq ft). Prefer oak or maple with professional installation.",
      category: "Flooring",
      budget: "$8,000 - $12,000",
      timeline: "2-3 weeks",
      location: "Berkeley, CA",
      postedDate: "2024-01-08",
      client: {
        name: "Lisa Rodriguez",
        rating: 4.7,
        reviewCount: 15,
        verified: false
      },
      requirements: ["Licensed contractor", "Insurance required"],
      bidsCount: 12,
      status: "in-review",
      urgency: "low"
    }
  ]

  const categories = [
    "All Categories",
    "Kitchen Remodeling",
    "Bathroom Renovation", 
    "Flooring",
    "Painting",
    "Electrical",
    "Plumbing",
    "Roofing",
    "Landscaping"
  ]

  const budgetRanges = [
    "All Budgets",
    "Under $5,000",
    "$5,000 - $15,000",
    "$15,000 - $30,000",
    "$30,000 - $50,000",
    "Over $50,000"
  ]

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high": return "bg-red-100 text-red-700"
      case "medium": return "bg-yellow-100 text-yellow-700"
      case "low": return "bg-green-100 text-green-700"
      default: return "bg-slate-100 text-slate-700"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open": return "bg-green-100 text-green-700"
      case "in-review": return "bg-yellow-100 text-yellow-700"
      case "awarded": return "bg-blue-100 text-blue-700"
      case "closed": return "bg-slate-100 text-slate-700"
      default: return "bg-slate-100 text-slate-700"
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || selectedCategory === "All Categories" || 
                           project.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <ProRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />
        
        <div className="container mx-auto px-6 py-8">
          <PageHeader
            title="Browse Projects"
            description="Find and bid on renovation projects that match your expertise"
          />

          {/* Search and Filters */}
          <div className="flex items-center gap-4 mb-6">
            {/* Search Bar */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-slate-200 focus:border-slate-300"
              />
            </div>

            {/* Quick Filters */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-slate-200 rounded-lg px-3 py-2 text-sm bg-white focus:border-slate-300 focus:outline-none"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={selectedBudget}
              onChange={(e) => setSelectedBudget(e.target.value)}
              className="border border-slate-200 rounded-lg px-3 py-2 text-sm bg-white focus:border-slate-300 focus:outline-none"
            >
              {budgetRanges.map((range) => (
                <option key={range} value={range}>
                  {range}
                </option>
              ))}
            </select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="border-slate-200 hover:border-slate-300"
            >
              <Filter className="h-4 w-4" />
            </Button>

            {/* Results Count */}
            <span className="text-xs text-slate-500 whitespace-nowrap">
              {filteredProjects.length} projects
            </span>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProjects.map((project) => (
              <Card key={project.id} className="p-6 hover:shadow-lg transition-shadow">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-slate-900 mb-2">
                        {project.title}
                      </h3>
                      <div className="flex items-center space-x-2 mb-3">
                        <Badge className={getStatusColor(project.status)}>
                          {project.status.replace('-', ' ')}
                        </Badge>
                        <Badge className={getUrgencyColor(project.urgency)}>
                          {project.urgency} priority
                        </Badge>
                        <Badge variant="outline">
                          {project.category}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <BookmarkButton
                        itemId={project.id}
                        itemType="project"
                        className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                      />
                      <LikeButton
                        itemId={project.id}
                        itemType="project"
                        className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                      />
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-600 text-sm line-clamp-3">
                    {project.description}
                  </p>

                  {/* Project Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2 text-slate-600">
                      <DollarSign className="h-4 w-4" />
                      <span>{project.budget}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-slate-600">
                      <Clock className="h-4 w-4" />
                      <span>{project.timeline}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-slate-600">
                      <MapPin className="h-4 w-4" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-slate-600">
                      <Calendar className="h-4 w-4" />
                      <span>Posted {project.postedDate}</span>
                    </div>
                  </div>

                  {/* Client Info */}
                  <div className="flex items-center justify-between pt-4 border-t border-slate-200">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900 text-sm">
                          {project.client.name}
                          {project.client.verified && (
                            <span className="ml-1 text-green-600">✓</span>
                          )}
                        </p>
                        <div className="flex items-center space-x-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-slate-600">
                            {project.client.rating} ({project.client.reviewCount} reviews)
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-slate-600">
                      {project.bidsCount} bids
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 pt-4">
                    <Button size="action">
                      Submit Bid
                    </Button>
                    <Button variant="outline" size="action">
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Projects
            </Button>
          </div>
        </div>
      </div>
    </ProRoute>
  )
}
