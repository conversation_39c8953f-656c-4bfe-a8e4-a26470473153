"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { useProjects } from "@/hooks/use-projects"
import { EnhancedCard, StatsCard, ProjectCard } from "@/components/ui/enhanced-card"
import { OnboardingTour, customerOnboardingSteps, useOnboarding } from "@/components/ui/onboarding-tour"
import { Plus, TrendingUp, Clock, CheckCircle, MessageCircle, Star, ArrowRight, Calendar, DollarSign, Hammer, HelpCircle } from "lucide-react"
import Link from "next/link"

export default function DashboardPage() {
  const { user } = useUser()
  const { isOnboardingOpen, setIsOnboardingOpen, completeOnboarding, startOnboarding } = useOnboarding()
  const { projects, loading: projectsLoading, error: projectsError } = useProjects({
    autoFetch: true,
    includeCompleted: false
  })

  // Calculate stats from real data
  const activeProjects = projects.filter(p => p.status === 'in-progress' || p.status === 'active')
  const completedProjects = projects.filter(p => p.status === 'completed')

  const stats = [
    { label: "Active Projects", value: activeProjects.length.toString(), icon: Clock, color: "text-blue-600" },
    { label: "Completed Projects", value: completedProjects.length.toString(), icon: CheckCircle, color: "text-green-600" },
    { label: "Total Projects", value: projects.length.toString(), icon: TrendingUp, color: "text-purple-600" },
    { label: "In Bidding", value: projects.filter(p => p.status === 'active').length.toString(), icon: Star, color: "text-yellow-600" },
  ]

  // Recent activity would come from a real activity feed service
  const recentActivity = []

  return (
    <CustomerRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-12 gap-6">
          <div className="space-y-2">
            <h1 className="text-2xl lg:text-3xl font-bold text-slate-900">
              Welcome back{user?.name ? `, ${user.name.split(' ')[0]}` : ''}
            </h1>
            <p className="text-lg text-slate-600 max-w-2xl">
              Here's what's happening with your renovation projects.
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={startOnboarding}
              size="sm"
              className="bg-transparent border-slate-200/60 hover:border-slate-300"
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Take Tour
            </Button>
            <Link href="/">
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Project
              </Button>
            </Link>
          </div>
        </div>

        {/* Clean Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg transition-all hover:shadow-xl hover:scale-[1.02]">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-500 mb-1">{stat.label}</p>
                  <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                </div>
                <div className="flex-shrink-0">
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Active Projects */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold text-slate-900">Active Projects</h2>
              <Link href="/projects">
                <Button variant="ghost" size="sm" className="text-slate-500 hover:text-slate-700">
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>

            <div className="space-y-4">
              {projectsLoading ? (
                // Loading skeleton
                Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg animate-pulse">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="w-5 h-5 bg-slate-200 rounded mt-1"></div>
                        <div className="flex-1">
                          <div className="h-6 bg-slate-200 rounded mb-2 w-3/4"></div>
                          <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="w-20 h-4 bg-slate-200 rounded"></div>
                    </div>
                  </div>
                ))
              ) : (
                activeProjects.map((project) => (
                  <div key={project.id} className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg transition-all hover:shadow-xl hover:scale-[1.02]">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 mt-1">
                          <Hammer className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-bold text-slate-900 mb-1">{project.title}</h3>
                          <div className="flex items-center space-x-4 text-sm text-slate-500">
                            <span className="flex items-center">
                              <div
                                className={`w-2 h-2 rounded-full mr-2 ${
                                  project.status === "in-progress" ? "bg-blue-500" :
                                  project.status === "active" ? "bg-yellow-500" : "bg-green-500"
                                }`}
                              />
                              {project.status === 'in-progress' ? 'In Progress' :
                               project.status === 'active' ? 'Bidding' :
                               project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                            </span>
                            {project.contractors?.business_name && <span>with {project.contractors.business_name}</span>}
                            <span>{project.category}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-slate-900">
                          {typeof project.budget === 'object' && project.budget ?
                            (project.budget as any).range || 'Budget set' :
                            'Budget TBD'}
                        </div>
                      </div>
                    </div>

                    {project.status === "in-progress" && project.milestones && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-slate-600">Progress</span>
                          <span className="text-sm font-medium text-slate-900">
                            {Array.isArray(project.milestones) ?
                              `${Math.round((project.milestones.filter((m: any) => m.completed).length / project.milestones.length) * 100)}%` :
                              '0%'}
                          </span>
                        </div>
                        <div className="w-full bg-slate-100 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: Array.isArray(project.milestones) ?
                                `${(project.milestones.filter((m: any) => m.completed).length / project.milestones.length) * 100}%` :
                                '0%'
                            }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-3">
                      {project.status === "active" ? (
                        <Link href={`/project/${project.id}/bids`}>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                            View Bids
                          </Button>
                        </Link>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            className="bg-transparent border-slate-200/60 hover:border-slate-300"
                          >
                            Message
                          </Button>
                          <Link href={`/project/${project.id}`}>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                              View Details
                            </Button>
                          </Link>
                        </>
                      )}
                    </div>
                  </div>
                ))
              )}

              {!projectsLoading && activeProjects.length === 0 && (
                <div className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-12 shadow-lg text-center">
                  <Hammer className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">No active projects</h3>
                  <p className="text-slate-500 mb-6">Start your first renovation project today</p>
                  <Link href="/">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">Create Project</Button>
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Recent Activity */}
            <div className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-slate-900 mb-4">Recent Activity</h3>

              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {activity.type === "milestone" ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : activity.type === "bid" ? (
                        <Clock className="h-5 w-5 text-blue-600" />
                      ) : (
                        <MessageCircle className="h-5 w-5 text-purple-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-slate-600 leading-relaxed mb-1">{activity.message}</p>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-slate-400">{activity.time}</span>
                        <span className="text-sm text-slate-400">•</span>
                        <span className="text-sm text-slate-500">{activity.project}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Button variant="ghost" size="sm" className="w-full mt-4 text-slate-500 hover:text-slate-700">
                View All Activity
              </Button>
            </div>



            {/* Upcoming */}
            <div className="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-slate-900 mb-4">Upcoming</h3>

              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">
                    <Calendar className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-slate-900 leading-relaxed mb-1">Cabinet Installation</p>
                    <span className="text-sm text-slate-400">Tomorrow, 9:00 AM</span>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">
                    <MessageCircle className="h-5 w-5 text-purple-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-slate-900 leading-relaxed mb-1">Project Review Call</p>
                    <span className="text-sm text-slate-400">Dec 18, 2:00 PM</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Onboarding Tour */}
      <OnboardingTour
        steps={customerOnboardingSteps}
        isOpen={isOnboardingOpen}
        onClose={() => setIsOnboardingOpen(false)}
        onComplete={completeOnboarding}
      />

      </div>
    </CustomerRoute>
  )
}
